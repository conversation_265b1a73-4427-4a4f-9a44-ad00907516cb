* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font: inherit;
  -webkit-tap-highlight-color: transparent;
  transition-behavior: allow-discrete;
  user-select: none;
}

::selection {
  background-color: inherit;
  color: inherit;
}

html {
  display: flex;
  overflow: hidden;
  width: 100dvw;
  height: 100dvh;
  align-items: center;
  justify-content: center;
  background:
    linear-gradient(
      45deg,
      #e6e6fa 25%,
      #f0f8ff 0,
      #f0f8ff 50%,
      #e6e6fa 0,
      #e6e6fa 75%,
      #f0f8ff 0)
    0% 0% / 3rem 3rem;
}

body {
  display: flex;
  height: 100%;
  max-height: 40rem;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  padding: 0;
  aspect-ratio: 9 / 16;
  gap: 1rem;
}

body.game {
  gap: 0.75rem;
}

header,
main {
  display: contents;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.visually-hidden {
  position: absolute;
  overflow: hidden;
  width: 1px;
  height: 1px;
  clip-path: inset(50%);
  white-space: nowrap;
}

.bubbles {
  position: absolute;
  z-index: 10;
  top: -0.5rem;
  left: 0;
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: center;
  gap: 0.25rem;
  white-space: nowrap;
}
