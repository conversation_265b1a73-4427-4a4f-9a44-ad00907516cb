-- CreateTable
CREATE TABLE "public"."hunts" (
    "id" SERIAL NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "hunts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."treasures" (
    "id" SERIAL NOT NULL,
    "huntId" INTEGER NOT NULL,
    "ordinal" INTEGER NOT NULL,
    "qrCodeData" VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "treasures_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."clues" (
    "id" SERIAL NOT NULL,
    "treasureId" INTEGER NOT NULL,
    "text" VARCHAR(200) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "clues_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "treasures_huntId_ordinal_key" ON "public"."treasures"("huntId", "ordinal");

-- CreateIndex
CREATE UNIQUE INDEX "clues_treasureId_key" ON "public"."clues"("treasureId");

-- AddForeignKey
ALTER TABLE "public"."treasures" ADD CONSTRAINT "treasures_huntId_fkey" FOREIGN KEY ("huntId") REFERENCES "public"."hunts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."clues" ADD CONSTRAINT "clues_treasureId_fkey" FOREIGN KEY ("treasureId") REFERENCES "public"."treasures"("id") ON DELETE CASCADE ON UPDATE CASCADE;
