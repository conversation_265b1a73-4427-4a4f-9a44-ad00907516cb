// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Hunt {
  id        Int      @id @default(autoincrement())
  title     String   @db.VarChar(255)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  treasures Treasure[]
  @@map("hunts")
}

model Treasure {
  id        Int      @id @default(autoincrement())
  huntId    Int
  hunt      Hunt     @relation(fields: [huntId], references: [id], onDelete: Cascade)
  ordinal   Int
  qrCodeData String  @db.VarChar(255)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  clue      Clue?
  @@map("treasures")
  @@unique([huntId, ordinal])
}

model Clue {
  id        Int      @id @default(autoincrement())
  treasureId Int     @unique
  treasure  Treasure @relation(fields: [treasureId], references: [id], onDelete: Cascade)
  text      String   @db.VarChar(200)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  @@map("clues")
}
